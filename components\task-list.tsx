"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { TaskItem } from "@/components/task-item"
import { useTasks } from "@/contexts/TaskContext"

export default function TaskList() {
  const { tasks, updateTaskStatus, toggleTaskImportant, getTodayTasks, getImportantTasks, isLoading } = useTasks()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  const todayTasks = getTodayTasks()
  const importantTasks = getImportantTasks()

  return (
    <Tabs defaultValue="all" className="w-full">
      <TabsList className="grid grid-cols-3 mb-6">
        <TabsTrigger value="all">All</TabsTrigger>
        <TabsTrigger value="today">Today</TabsTrigger>
        <TabsTrigger value="important">Important</TabsTrigger>
      </TabsList>

      <TabsContent value="all" className="space-y-4">
        {tasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No tasks yet. Create your first task!</p>
          </div>
        ) : (
          tasks.map((task) => (
            <TaskItem
              key={task.id}
              task={task}
              onStatusChange={(status) => updateTaskStatus(task.id, status as any)}
              onToggleImportant={() => toggleTaskImportant(task.id)}
            />
          ))
        )}
      </TabsContent>

      <TabsContent value="today" className="space-y-4">
        {todayTasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No tasks for today. Add some tasks to get started!</p>
          </div>
        ) : (
          todayTasks.map((task) => (
            <TaskItem
              key={task.id}
              task={task}
              onStatusChange={(status) => updateTaskStatus(task.id, status as any)}
              onToggleImportant={() => toggleTaskImportant(task.id)}
            />
          ))
        )}
      </TabsContent>

      <TabsContent value="important" className="space-y-4">
        {importantTasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No important tasks. Mark tasks as important to see them here!</p>
          </div>
        ) : (
          importantTasks.map((task) => (
            <TaskItem
              key={task.id}
              task={task}
              onStatusChange={(status) => updateTaskStatus(task.id, status as any)}
              onToggleImportant={() => toggleTaskImportant(task.id)}
            />
          ))
        )}
      </TabsContent>
    </Tabs>
  )
}
