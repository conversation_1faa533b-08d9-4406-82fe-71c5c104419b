// Authentication utilities for InsForge
const PROJECT_ID = process.env.NEXT_PUBLIC_INSFORGE_PROJECT_ID!
const BASE_URL = 'https://insforge-backend-740c116fd723.herokuapp.com/project'

export interface User {
  user_id: string
  email: string
  name: string
  project_id: string
}

export interface AuthResponse {
  token: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface SignupCredentials {
  name: string
  email: string
  password: string
}

// JWT token utilities
export const tokenUtils = {
  // Store token in localStorage
  setToken: (token: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token)
    }
  },

  // Get token from localStorage
  getToken: (): string | null => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token')
    }
    return null
  },

  // Remove token from localStorage
  removeToken: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token')
    }
  },

  // Decode JWT token to get user info
  decodeToken: (token: string): User | null => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return {
        user_id: payload.user_id,
        email: payload.email,
        name: payload.name,
        project_id: payload.project_id
      }
    } catch (error) {
      console.error('Error decoding token:', error)
      return null
    }
  },

  // Check if token is expired
  isTokenExpired: (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Date.now() / 1000
      return payload.exp < currentTime
    } catch (error) {
      return true
    }
  }
}

// Authentication API functions
export const authAPI = {
  // User signup
  signup: async (credentials: SignupCredentials): Promise<AuthResponse> => {
    const response = await fetch(`${BASE_URL}/${PROJECT_ID}/sign-up`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(error || 'Signup failed')
    }

    return response.json()
  },

  // User login
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await fetch(`${BASE_URL}/${PROJECT_ID}/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(error || 'Login failed')
    }

    return response.json()
  },

  // Verify/refresh token
  verifyToken: async (token: string): Promise<AuthResponse> => {
    const response = await fetch(`${BASE_URL}/${PROJECT_ID}/verify-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token }),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(error || 'Token verification failed')
    }

    return response.json()
  }
}
