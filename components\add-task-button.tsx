"use client"

import type React from "react"

import { useState } from "react"
import { Plus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@/components/date-picker"
import { useTasks } from "@/contexts/TaskContext"
import { useToast } from "@/hooks/use-toast"

export function AddTaskButton() {
  const [open, setOpen] = useState(false)
  const [taskTitle, setTaskTitle] = useState("")
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [important, setImportant] = useState(false)
  const { addTask } = useTasks()
  const { toast } = useToast()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!taskTitle.trim()) {
      toast({
        title: "Task title required",
        description: "Please enter a task title.",
        variant: "destructive",
      })
      return
    }

    if (!date) {
      toast({
        title: "Date required",
        description: "Please select a due date.",
        variant: "destructive",
      })
      return
    }

    // Capitalize first letter
    const capitalizedTitle = taskTitle.charAt(0).toUpperCase() + taskTitle.slice(1)

    // Add the task using the context
    addTask(capitalizedTitle, date, important)

    toast({
      title: "Task created",
      description: `"${capitalizedTitle}" has been added to your tasks.`,
    })

    // Reset form and close dialog
    setTaskTitle("")
    setDate(new Date())
    setImportant(false)
    setOpen(false)
  }

  return (
    <>
      <Button
        size="icon"
        className="fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg"
        onClick={() => setOpen(true)}
      >
        <Plus className="h-6 w-6" />
        <span className="sr-only">Add Task</span>
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Task</DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="task-title">Task Title</Label>
              <Input
                id="task-title"
                value={taskTitle}
                onChange={(e) => setTaskTitle(e.target.value)}
                placeholder="Enter your task"
                required
              />
            </div>

            <div className="space-y-2">
              <Label>Due Date</Label>
              <DatePicker date={date} setDate={setDate} />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="important"
                checked={important}
                onCheckedChange={(checked) => setImportant(checked as boolean)}
              />
              <Label htmlFor="important" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Mark as important
              </Label>
            </div>

            <Button type="submit" className="w-full">
              Add Task
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    </>
  )
}
