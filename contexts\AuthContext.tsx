"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, authAPI, tokenUtils } from '@/lib/auth'

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  signup: (name: string, email: string, password: string) => Promise<void>
  logout: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      const token = tokenUtils.getToken()
      
      if (!token) {
        setIsLoading(false)
        return
      }

      // Check if token is expired
      if (tokenUtils.isTokenExpired(token)) {
        tokenUtils.removeToken()
        setIsLoading(false)
        return
      }

      try {
        // Verify token with server
        const response = await authAPI.verifyToken(token)
        const userData = tokenUtils.decodeToken(response.token)
        
        if (userData) {
          setUser(userData)
          tokenUtils.setToken(response.token) // Update with fresh token
        } else {
          tokenUtils.removeToken()
        }
      } catch (error) {
        console.error('Token verification failed:', error)
        tokenUtils.removeToken()
      } finally {
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const login = async (email: string, password: string) => {
    try {
      const response = await authAPI.login({ email, password })
      const userData = tokenUtils.decodeToken(response.token)
      
      if (userData) {
        tokenUtils.setToken(response.token)
        setUser(userData)
      } else {
        throw new Error('Invalid token received')
      }
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const signup = async (name: string, email: string, password: string) => {
    try {
      const response = await authAPI.signup({ name, email, password })
      const userData = tokenUtils.decodeToken(response.token)
      
      if (userData) {
        tokenUtils.setToken(response.token)
        setUser(userData)
      } else {
        throw new Error('Invalid token received')
      }
    } catch (error) {
      console.error('Signup failed:', error)
      throw error
    }
  }

  const logout = () => {
    tokenUtils.removeToken()
    setUser(null)
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    signup,
    logout
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
