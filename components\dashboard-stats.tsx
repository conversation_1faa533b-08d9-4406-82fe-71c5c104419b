"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card"
import { CheckCircle, Calendar, Clock, Star } from "lucide-react"
import { useTasks } from "@/contexts/TaskContext"

export function DashboardStats() {
  const { tasks, getTodayTasks, getImportantTasks } = useTasks()

  const completedTasks = tasks.filter(task => task.status === 'completed').length
  const todayTasks = getTodayTasks().length
  const importantTasks = getImportantTasks().length
  const remainingTasks = tasks.filter(task => task.status !== 'completed').length

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Tasks Completed</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{completedTasks}</div>
          <p className="text-xs text-muted-foreground">Total completed tasks</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Today's Tasks</CardTitle>
          <Calendar className="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{todayTasks}</div>
          <p className="text-xs text-muted-foreground">Tasks due today</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Important Tasks</CardTitle>
          <Star className="h-4 w-4 text-yellow-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{importantTasks}</div>
          <p className="text-xs text-muted-foreground">Marked as important</p>
        </CardContent>
      </Card>
    </>
  )
}
