"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

export interface Task {
  id: string
  title: string
  status: 'not-started' | 'in-progress' | 'completed'
  date: Date
  important: boolean
  userId: string
  createdAt: Date
}

interface TaskContextType {
  tasks: Task[]
  addTask: (title: string, date: Date, important?: boolean) => void
  updateTask: (id: string, updates: Partial<Task>) => void
  deleteTask: (id: string) => void
  updateTaskStatus: (id: string, status: Task['status']) => void
  toggleTaskImportant: (id: string) => void
  getTodayTasks: () => Task[]
  getImportantTasks: () => Task[]
  isLoading: boolean
}

const TaskContext = createContext<TaskContextType | undefined>(undefined)

// Initial mock tasks for demonstration
const INITIAL_TASKS: Task[] = [
  {
    id: '1',
    title: 'Complete project proposal',
    status: 'in-progress',
    date: new Date(),
    important: false,
    userId: 'demo',
    createdAt: new Date()
  },
  {
    id: '2',
    title: 'Schedule team meeting',
    status: 'not-started',
    date: new Date(),
    important: true,
    userId: 'demo',
    createdAt: new Date()
  },
  {
    id: '3',
    title: 'Review client feedback',
    status: 'completed',
    date: new Date(),
    important: false,
    userId: 'demo',
    createdAt: new Date()
  },
  {
    id: '4',
    title: 'Update documentation',
    status: 'not-started',
    date: new Date(),
    important: false,
    userId: 'demo',
    createdAt: new Date()
  }
]

export function TaskProvider({ children }: { children: React.ReactNode }) {
  const [tasks, setTasks] = useState<Task[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { user, isAuthenticated } = useAuth()

  // Load tasks from localStorage on mount
  useEffect(() => {
    if (isAuthenticated && user) {
      const savedTasks = localStorage.getItem(`tasks_${user.user_id}`)
      if (savedTasks) {
        try {
          const parsedTasks = JSON.parse(savedTasks).map((task: any) => ({
            ...task,
            date: new Date(task.date),
            createdAt: new Date(task.createdAt)
          }))
          setTasks(parsedTasks)
        } catch (error) {
          console.error('Error parsing saved tasks:', error)
          setTasks(INITIAL_TASKS.map(task => ({ ...task, userId: user.user_id })))
        }
      } else {
        // Set initial tasks for new users
        const userTasks = INITIAL_TASKS.map(task => ({ ...task, userId: user.user_id }))
        setTasks(userTasks)
        localStorage.setItem(`tasks_${user.user_id}`, JSON.stringify(userTasks))
      }
      setIsLoading(false)
    } else {
      setTasks([])
      setIsLoading(false)
    }
  }, [isAuthenticated, user])

  // Save tasks to localStorage whenever tasks change
  useEffect(() => {
    if (isAuthenticated && user && tasks.length > 0) {
      localStorage.setItem(`tasks_${user.user_id}`, JSON.stringify(tasks))
    }
  }, [tasks, isAuthenticated, user])

  const addTask = (title: string, date: Date, important: boolean = false) => {
    if (!user) return

    const newTask: Task = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      title: title.trim(),
      status: 'not-started',
      date,
      important,
      userId: user.user_id,
      createdAt: new Date()
    }

    setTasks(prevTasks => [...prevTasks, newTask])
  }

  const updateTask = (id: string, updates: Partial<Task>) => {
    setTasks(prevTasks =>
      prevTasks.map(task =>
        task.id === id ? { ...task, ...updates } : task
      )
    )
  }

  const deleteTask = (id: string) => {
    setTasks(prevTasks => prevTasks.filter(task => task.id !== id))
  }

  const updateTaskStatus = (id: string, status: Task['status']) => {
    updateTask(id, { status })
  }

  const toggleTaskImportant = (id: string) => {
    setTasks(prevTasks =>
      prevTasks.map(task =>
        task.id === id ? { ...task, important: !task.important } : task
      )
    )
  }

  const getTodayTasks = () => {
    const today = new Date()
    return tasks.filter(task => {
      const taskDate = new Date(task.date)
      return taskDate.toDateString() === today.toDateString()
    })
  }

  const getImportantTasks = () => {
    return tasks.filter(task => task.important)
  }

  const value: TaskContextType = {
    tasks,
    addTask,
    updateTask,
    deleteTask,
    updateTaskStatus,
    toggleTaskImportant,
    getTodayTasks,
    getImportantTasks,
    isLoading
  }

  return (
    <TaskContext.Provider value={value}>
      {children}
    </TaskContext.Provider>
  )
}

export function useTasks() {
  const context = useContext(TaskContext)
  if (context === undefined) {
    throw new Error('useTasks must be used within a TaskProvider')
  }
  return context
}
